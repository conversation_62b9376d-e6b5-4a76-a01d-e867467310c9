import { Building, Users, Award, Clock } from 'lucide-react';
import { COMPANY_INFO, VALUE_PROPOSITIONS } from '@/lib/constants';

export default function AboutPage() {
  const stats = [
    { number: "10+", label: "Years Experience", icon: Clock },
    { number: "100+", label: "Projects Completed", icon: Award },
    { number: "50+", label: "Happy Clients", icon: Users },
    { number: "19+", label: "Technology Partners", icon: Building },
  ];

  return (
    <div className="pt-20">
      {/* Hero Section */}
      <section className="py-32 gradient-mesh relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-20 left-1/4 w-96 h-96 bg-white/5 rounded-full blur-3xl animate-pulse-soft"></div>
          <div className="absolute bottom-20 right-1/4 w-80 h-80 bg-white/3 rounded-full blur-3xl animate-pulse-soft animation-delay-1000"></div>
        </div>
        
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="max-w-5xl mx-auto text-center">
            <h1 className="text-5xl sm:text-6xl lg:text-7xl font-bold mb-8">
              <span className="text-gradient">
                About {COMPANY_INFO.name}
              </span>
            </h1>
            <p className="text-xl sm:text-2xl text-gray-300 mb-8 font-light leading-relaxed">
              {COMPANY_INFO.description}
            </p>
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-32 gradient-subtle relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-32 right-20 w-72 h-72 bg-white/5 rounded-full blur-3xl animate-float"></div>
          <div className="absolute bottom-32 left-20 w-64 h-64 bg-white/3 rounded-full blur-2xl animate-float animation-delay-2000"></div>
        </div>
        
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div className="glass-strong rounded-3xl p-12 card-modern hover:scale-105 transition-all duration-500">
              <h2 className="text-4xl font-bold text-gradient mb-8">Our Mission</h2>
              <p className="text-gray-300 mb-6 leading-relaxed text-lg">
                To empower businesses with cutting-edge technology solutions that drive growth, 
                enhance efficiency, and secure digital assets. We are committed to delivering 
                exceptional IT services that transform how organizations operate in the digital age.
              </p>
              <p className="text-gray-400 leading-relaxed">
                Our comprehensive approach combines deep technical expertise with industry best 
                practices to ensure our clients achieve their technology goals and maintain 
                competitive advantages in their respective markets.
              </p>
            </div>
            <div className="glass-strong rounded-3xl p-12 card-modern hover:scale-105 transition-all duration-500">
              <h2 className="text-4xl font-bold text-gradient mb-8">Our Vision</h2>
              <p className="text-gray-300 mb-6 leading-relaxed text-lg">
                To be the leading technology partner for businesses seeking reliable, innovative, 
                and scalable IT solutions. We envision a future where technology seamlessly 
                integrates with business operations to create unprecedented opportunities for growth.
              </p>
              <p className="text-gray-400 leading-relaxed">
                Through continuous innovation and strategic partnerships with industry leaders, 
                we aim to shape the future of enterprise technology and help our clients thrive 
                in an increasingly digital world.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Statistics */}
      <section className="py-32 gradient-dark relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute inset-0 opacity-15">
          <div className="absolute top-20 left-20 w-80 h-80 bg-white/5 rounded-full blur-3xl animate-pulse-soft"></div>
          <div className="absolute bottom-20 right-20 w-72 h-72 bg-white/3 rounded-full blur-3xl animate-pulse-soft animation-delay-1500"></div>
        </div>
        
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="text-center mb-20">
            <h2 className="text-4xl sm:text-5xl font-bold text-gradient mb-6">Our Track Record</h2>
            <p className="text-xl text-gray-400 font-light">Numbers that speak for our expertise</p>
          </div>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => {
              const IconComponent = stat.icon;
              return (
                <div key={index} className="glass-strong rounded-2xl p-10 text-center card-modern hover:scale-105 transition-all duration-500 group">
                  <div className="w-16 h-16 glass rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>
                  <div className="text-4xl font-bold text-gradient mb-3 group-hover:scale-105 transition-transform">{stat.number}</div>
                  <div className="text-gray-400 group-hover:text-gray-300 transition-colors">{stat.label}</div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Value Propositions */}
      <section className="py-32 gradient-subtle relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-32 right-32 w-96 h-96 bg-white/5 rounded-full blur-3xl animate-float"></div>
          <div className="absolute bottom-32 left-32 w-80 h-80 bg-white/3 rounded-full blur-2xl animate-float animation-delay-2000"></div>
        </div>
        
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="text-center mb-20">
            <h2 className="text-4xl sm:text-5xl font-bold text-gradient mb-6">Why Choose S2P Infotech?</h2>
            <p className="text-xl text-gray-400 font-light">Our commitment to excellence drives everything we do</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {VALUE_PROPOSITIONS.map((value, index) => (
              <div key={index} className="glass-strong rounded-2xl p-10 text-center card-modern hover:scale-105 transition-all duration-500 group">
                <div className="w-16 h-16 glass rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <Award className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gradient mb-4">{value.title}</h3>
                <p className="text-gray-400 group-hover:text-gray-300 transition-colors leading-relaxed">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-32 gradient-dark relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-20 left-1/3 w-96 h-96 bg-white/5 rounded-full blur-3xl animate-pulse-soft"></div>
          <div className="absolute bottom-20 right-1/3 w-80 h-80 bg-white/3 rounded-full blur-3xl animate-pulse-soft animation-delay-1000"></div>
        </div>
        
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center relative">
          <h2 className="text-4xl sm:text-5xl font-bold text-gradient mb-8">Ready to Partner with Us?</h2>
          <p className="text-xl text-gray-300 mb-12 max-w-3xl mx-auto font-light leading-relaxed">
            Let's discuss how we can help transform your business with our comprehensive IT solutions.
          </p>
          <button className="btn-modern bg-white text-black px-12 py-4 rounded-full font-semibold hover:bg-gray-100 transition-all duration-300 text-lg shadow-2xl hover:shadow-white/30 hover:scale-105">
            Get in Touch
          </button>
        </div>
      </section>
    </div>
  );
}
