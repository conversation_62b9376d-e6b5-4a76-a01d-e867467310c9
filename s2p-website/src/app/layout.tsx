import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import Header from "@/components/layout/header";
import Footer from "@/components/layout/footer-new";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "S2P Infotech - Empowering Businesses with Cutting-Edge IT Solutions",
  description: "Your trusted partner for comprehensive technology services including IT consulting, software solutions, cybersecurity, and hardware infrastructure.",
  keywords: "IT services, technology consulting, cybersecurity, software solutions, enterprise IT, hardware infrastructure",
  authors: [{ name: "S2P Infotech" }],
  openGraph: {
    title: "S2P Infotech - Empowering Businesses with Cutting-Edge IT Solutions",
    description: "Your trusted partner for comprehensive technology services",
    type: "website",
    locale: "en_US",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <body className={`${inter.variable} font-sans antialiased min-h-screen bg-black text-white`}>
        <div className="flex flex-col min-h-screen">
          <Header />
          <main className="flex-grow pt-20">{children}</main>
          <Footer />
        </div>
      </body>
    </html>
  );
}
