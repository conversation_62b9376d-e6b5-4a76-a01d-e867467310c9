import { Building, Factory, Heart, GraduationCap, Shield, Zap } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

const solutions = [
  {
    title: "Healthcare IT Solutions",
    description: "HIPAA-compliant IT infrastructure and EMR systems for healthcare providers",
    icon: Heart,
    features: ["Electronic Medical Records", "HIPAA Compliance", "Telemedicine Systems", "Medical Device Integration"],
    industries: ["Hospitals", "Clinics", "Dental Practices", "Medical Labs"]
  },
  {
    title: "Financial Services Technology",
    description: "Secure banking and financial technology solutions with regulatory compliance",
    icon: Shield,
    features: ["Core Banking Systems", "Payment Processing", "Risk Management", "Compliance Reporting"],
    industries: ["Banks", "Credit Unions", "Insurance Companies", "Investment Firms"]
  },
  {
    title: "Manufacturing & Industrial",
    description: "Industrial IoT and automation solutions for manufacturing operations",
    icon: Factory,
    features: ["Industrial IoT", "Process Automation", "Quality Management", "Supply Chain Integration"],
    industries: ["Automotive", "Aerospace", "Electronics", "Chemical Processing"]
  },
  {
    title: "Education Technology",
    description: "Comprehensive EdTech solutions for educational institutions and e-learning",
    icon: GraduationCap,
    features: ["Learning Management Systems", "Student Information Systems", "Digital Classrooms", "Campus Networks"],
    industries: ["K-12 Schools", "Universities", "Training Centers", "Online Education"]
  },
  {
    title: "Enterprise Business Solutions",
    description: "Scalable enterprise software and infrastructure for large organizations",
    icon: Building,
    features: ["ERP Systems", "CRM Integration", "Business Intelligence", "Workflow Automation"],
    industries: ["Fortune 500", "Government", "Non-Profits", "Professional Services"]
  },
  {
    title: "Energy & Utilities",
    description: "Smart grid and energy management solutions for utility companies",
    icon: Zap,
    features: ["Smart Grid Technology", "Energy Management", "SCADA Systems", "Asset Management"],
    industries: ["Electric Utilities", "Gas Companies", "Water Utilities", "Renewable Energy"]
  }
];

export default function SolutionsPage() {
  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-blue-50 to-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">
              Industry Solutions
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Tailored technology solutions designed for specific industry requirements and challenges
            </p>
          </div>
        </div>
      </section>

      {/* Solutions Grid */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {solutions.map((solution, index) => {
              const IconComponent = solution.icon;
              
              return (
                <Card key={solution.title} className="p-8 hover:shadow-lg transition-all duration-300">
                  <div className="flex items-start space-x-4">
                    <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                      <IconComponent className="w-8 h-8 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-2xl font-semibold text-gray-900 mb-3">
                        {solution.title}
                      </h3>
                      <p className="text-gray-600 mb-4 leading-relaxed">
                        {solution.description}
                      </p>
                      
                      <div className="mb-4">
                        <h4 className="text-sm font-semibold text-gray-900 mb-2">Key Features:</h4>
                        <ul className="grid grid-cols-2 gap-1 text-sm text-gray-600">
                          {solution.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center">
                              <span className="w-1.5 h-1.5 bg-blue-600 rounded-full mr-2"></span>
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </div>
                      
                      <div className="mb-6">
                        <h4 className="text-sm font-semibold text-gray-900 mb-2">Industries Served:</h4>
                        <div className="flex flex-wrap gap-2">
                          {solution.industries.map((industry, idx) => (
                            <span key={idx} className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full">
                              {industry}
                            </span>
                          ))}
                        </div>
                      </div>
                      
                      <Button variant="outline">
                        Learn More
                      </Button>
                    </div>
                  </div>
                </Card>
              );
            })}
          </div>
        </div>
      </section>

      {/* Case Studies */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Success Stories</h2>
            <p className="text-xl text-gray-600">Real results from our industry solutions</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="p-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">40%</div>
                <div className="text-sm text-gray-600 mb-4">Reduction in IT Costs</div>
                <p className="text-gray-700 text-sm">
                  Major healthcare system reduced operational costs through our integrated EMR solution
                </p>
              </div>
            </Card>
            
            <Card className="p-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 mb-2">99.9%</div>
                <div className="text-sm text-gray-600 mb-4">System Uptime</div>
                <p className="text-gray-700 text-sm">
                  Financial institution achieved near-perfect uptime with our banking infrastructure
                </p>
              </div>
            </Card>
            
            <Card className="p-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600 mb-2">300%</div>
                <div className="text-sm text-gray-600 mb-4">Productivity Increase</div>
                <p className="text-gray-700 text-sm">
                  Manufacturing client tripled production efficiency with our IoT automation solution
                </p>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* Implementation Process */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Implementation Process</h2>
            <p className="text-xl text-gray-600">From consultation to deployment and support</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-5 gap-8">
            {[
              { step: "1", title: "Assessment", description: "Analyze current systems and requirements" },
              { step: "2", title: "Design", description: "Create tailored solution architecture" },
              { step: "3", title: "Development", description: "Build and customize the solution" },
              { step: "4", title: "Testing", description: "Rigorous testing and quality assurance" },
              { step: "5", title: "Deployment", description: "Go-live with ongoing support" }
            ].map((phase, index) => (
              <div key={index} className="text-center">
                <div className="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center text-lg font-bold mx-auto mb-4">
                  {phase.step}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{phase.title}</h3>
                <p className="text-gray-600 text-sm">{phase.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 bg-blue-600 text-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-6">Ready to Transform Your Industry?</h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Contact our industry experts to discuss how we can create a customized solution for your specific needs.
          </p>
          <Button size="lg" variant="secondary">
            Schedule Industry Consultation
          </Button>
        </div>
      </section>
    </div>
  );
}
