import Image from 'next/image';
import { PARTNERS, PARTNER_CATEGORIES } from '@/lib/constants';
import PartnersSection from '@/components/sections/partners-section';

export default function PartnersPage() {
  const partnersByCategory = PARTNER_CATEGORIES.slice(1).reduce((acc, category) => {
    acc[category] = PARTNERS.filter(partner => partner.category === category);
    return acc;
  }, {} as Record<string, typeof PARTNERS>);

  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-blue-50 to-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">
              Our Technology Partners
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              We collaborate with industry leaders to deliver best-in-class solutions and services
            </p>
          </div>
        </div>
      </section>

      {/* Interactive Partners Section */}
      <PartnersSection />

      {/* Partners by Category */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          {Object.entries(partnersByCategory).map(([category, partners]) => (
            <div key={category} className="mb-16">
              <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
                {category} Partners
              </h2>
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-6">
                {partners.map((partner, index) => (
                  <div
                    key={`${partner.name}-${index}`}
                    className="bg-gray-50 rounded-lg p-6 hover:bg-white hover:shadow-md transition-all duration-300 flex items-center justify-center"
                  >
                    <Image
                      src={partner.logo}
                      alt={partner.name}
                      width={120}
                      height={60}
                      className="max-w-full max-h-12 object-contain"
                    />
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Partnership Benefits */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Partnership Benefits</h2>
            <p className="text-xl text-gray-600">What our partnerships mean for your business</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🏆</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Certified Expertise</h3>
              <p className="text-gray-600">
                Our team holds official certifications from all major technology partners, 
                ensuring expert-level implementation and support.
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">💰</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Cost Savings</h3>
              <p className="text-gray-600">
                Direct partnerships enable us to offer competitive pricing and exclusive 
                discounts on software licenses and hardware procurement.
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🚀</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Latest Technology</h3>
              <p className="text-gray-600">
                Early access to new releases and beta programs keeps your business 
                at the forefront of technological innovation.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Become a Partner CTA */}
      <section className="py-20 bg-blue-600 text-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-6">Interested in Partnership?</h2>
            <p className="text-xl text-blue-100 mb-8">
              Are you a technology vendor interested in partnering with S2P Infotech? 
              We're always looking to expand our ecosystem of trusted partners.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                Partnership Inquiry
              </button>
              <button className="border border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors">
                View Partnership Program
              </button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
