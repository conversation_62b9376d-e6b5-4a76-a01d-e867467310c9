import { 
  Settings, 
  Code, 
  Shield, 
  Server, 
  Paintbrush, 
  Eye, 
  Network, 
  Headphones,
  CheckCircle,
  ArrowRight
} from 'lucide-react';
import { SERVICES } from '@/lib/constants';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';

const iconMap = {
  settings: Settings,
  code: Code,
  shield: Shield,
  server: Server,
  paintbrush: Paintbrush,
  eye: Eye,
  network: Network,
  headphones: Headphones,
};

const serviceDetails = {
  "IT Consulting": {
    features: ["Technology Strategy", "Digital Transformation", "IT Architecture", "Project Management"],
    description: "Our IT consulting services help organizations align technology with business objectives, optimize IT infrastructure, and develop comprehensive digital transformation strategies."
  },
  "Software Solutions": {
    features: ["Enterprise Software", "Custom Development", "System Integration", "Migration Services"],
    description: "We provide comprehensive software solutions including enterprise application deployment, custom development, and seamless system integration services."
  },
  "Cybersecurity": {
    features: ["Security Assessment", "Threat Monitoring", "Compliance", "Incident Response"],
    description: "Protect your digital assets with our comprehensive cybersecurity solutions including threat detection, compliance management, and incident response services."
  },
  "Hardware & Infrastructure": {
    features: ["Server Solutions", "Network Design", "Cloud Infrastructure", "Hardware Procurement"],
    description: "Build robust IT infrastructure with our hardware and networking solutions designed for scalability, reliability, and optimal performance."
  },
  "CAD/Design Solutions": {
    features: ["CAD Software", "Design Training", "3D Modeling", "Engineering Solutions"],
    description: "Professional CAD and design solutions for architects, engineers, and designers with comprehensive training and support services."
  },
  "Surveillance Systems": {
    features: ["CCTV Installation", "Access Control", "Video Analytics", "Remote Monitoring"],
    description: "Complete surveillance and security system integration with advanced video analytics and remote monitoring capabilities."
  },
  "Networking Solutions": {
    features: ["Network Design", "WiFi Solutions", "VPN Setup", "Network Security"],
    description: "Design and implement robust networking solutions for seamless connectivity and secure data transmission across your organization."
  },
  "Support & Maintenance": {
    features: ["24/7 Support", "Preventive Maintenance", "Remote Assistance", "On-site Service"],
    description: "Comprehensive technical support and maintenance services to ensure your IT systems operate at peak performance around the clock."
  }
};

export default function ServicesPage() {
  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-blue-50 to-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">
              Our Services
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Comprehensive IT solutions designed to empower your business with cutting-edge technology
            </p>
          </div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {SERVICES.map((service, index) => {
              const IconComponent = iconMap[service.icon as keyof typeof iconMap];
              const details = serviceDetails[service.title as keyof typeof serviceDetails];
              
              return (
                <Card key={service.title} className="p-8 hover:shadow-lg transition-all duration-300">
                  <div className="flex items-start space-x-4">
                    <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                      <IconComponent className="w-8 h-8 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-2xl font-semibold text-gray-900 mb-3">
                        {service.title}
                      </h3>
                      <p className="text-gray-600 mb-4 leading-relaxed">
                        {details?.description || service.description}
                      </p>
                      <ul className="space-y-2 mb-6">
                        {details?.features.map((feature, idx) => (
                          <li key={idx} className="flex items-center space-x-2 text-sm text-gray-600">
                            <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                            <span>{feature}</span>
                          </li>
                        ))}
                      </ul>
                      <Button variant="outline" className="group">
                        Learn More
                        <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                      </Button>
                    </div>
                  </div>
                </Card>
              );
            })}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Process</h2>
            <p className="text-xl text-gray-600">How we deliver exceptional results</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {[
              {
                step: "01",
                title: "Discovery",
                description: "We analyze your current infrastructure and understand your business requirements"
              },
              {
                step: "02", 
                title: "Planning",
                description: "Develop a comprehensive strategy tailored to your specific needs and goals"
              },
              {
                step: "03",
                title: "Implementation",
                description: "Execute the solution with minimal disruption to your business operations"
              },
              {
                step: "04",
                title: "Support",
                description: "Provide ongoing support and maintenance to ensure optimal performance"
              }
            ].map((process, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">
                  {process.step}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{process.title}</h3>
                <p className="text-gray-600">{process.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 bg-blue-600 text-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-6">Ready to Get Started?</h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Contact us today to discuss your IT requirements and discover how our services can transform your business.
          </p>
          <Button size="lg" variant="secondary">
            Request a Consultation
          </Button>
        </div>
      </section>
    </div>
  );
}
