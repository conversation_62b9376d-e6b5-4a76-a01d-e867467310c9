import Link from 'next/link';
import Image from 'next/image';
import { Mail, Phone, MapPin, Globe, Linkedin, Twitter, Facebook, Instagram } from 'lucide-react';
import { COMPANY_INFO, NAVIGATION_ITEMS, SERVICES } from '@/lib/constants';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';

export default function Footer() {
  return (
    <footer className="relative border-t border-neutral-800 overflow-hidden bg-black">
      {/* Background elements */}
      <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center [mask-image:linear-gradient(180deg,rgba(255,255,255,0.03),rgba(255,255,255,0))] opacity-5 -z-10"></div>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 pt-16 pb-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 mb-16">
          {/* Company Info */}
          <div className="lg:col-span-1">
            <Link href="/" className="flex items-center space-x-3 mb-6" legacyBehavior>
              <div className="relative">
                <Image
                  src="/logo.svg"
                  alt={COMPANY_INFO.name}
                  width={40}
                  height={40}
                  className="w-10 h-10"
                />
              </div>
              <span className="text-xl font-bold tracking-tight">
                {COMPANY_INFO.name}
              </span>
            </Link>
            
            <p className="text-neutral-400 text-sm mb-6 leading-relaxed">
              {COMPANY_INFO.description}
            </p>
            
            <ul className="space-y-2">
              <li>
                <Link
                  href={`mailto:${COMPANY_INFO.email}`}
                  className="flex items-center text-sm text-neutral-400 hover:text-white transition-colors"
                  legacyBehavior>
                  <Mail size={16} className="mr-3 text-indigo-500" />
                  {COMPANY_INFO.email}
                </Link>
              </li>
              <li>
                <Link
                  href={`tel:${COMPANY_INFO.phone}`}
                  className="flex items-center text-sm text-neutral-400 hover:text-white transition-colors"
                  legacyBehavior>
                  <Phone size={16} className="mr-3 text-indigo-500" />
                  {COMPANY_INFO.phone}
                </Link>
              </li>
              <li className="flex items-start text-sm text-neutral-400">
                <MapPin size={16} className="mr-3 text-indigo-500 mt-1 flex-shrink-0" />
                <span>{COMPANY_INFO.address}</span>
              </li>
            </ul>
          </div>
          
          {/* Quick Links */}
          <div className="lg:col-span-1">
            <h3 className="text-lg font-semibold mb-6">Quick Links</h3>
            <ul className="space-y-2">
              {NAVIGATION_ITEMS.map((item) => (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    className="text-neutral-400 hover:text-white transition-colors text-sm"
                    legacyBehavior>
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
          
          {/* Services Links */}
          <div className="lg:col-span-1">
            <h3 className="text-lg font-semibold mb-6">Our Services</h3>
            <ul className="space-y-2">
              {SERVICES.slice(0, 6).map((service) => (
                <li key={service.title}>
                  <Link
                    href={`/services#${service.title.toLowerCase().replace(/\s+/g, '-')}`}
                    className="text-neutral-400 hover:text-white transition-colors text-sm"
                    legacyBehavior>
                    {service.title}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
          
          {/* Newsletter */}
          <div className="lg:col-span-1">
            <h3 className="text-lg font-semibold mb-6">Stay Updated</h3>
            <p className="text-neutral-400 text-sm mb-4">
              Subscribe to our newsletter to get the latest updates and news.
            </p>
            
            <div className="flex flex-col space-y-3">
              <Input 
                type="email" 
                placeholder="Your email address" 
                className="bg-neutral-900 border-neutral-800 focus-visible:ring-indigo-500"
              />
              <Button 
                className="bg-gradient-to-r from-indigo-500 to-violet-600 hover:from-indigo-600 hover:to-violet-700"
              >
                Subscribe
              </Button>
            </div>
            
            <div className="flex items-center space-x-4 mt-6">
              <Link
                href="#"
                className="text-neutral-400 hover:text-white transition-colors"
                legacyBehavior>
                <Linkedin size={20} />
              </Link>
              <Link
                href="#"
                className="text-neutral-400 hover:text-white transition-colors"
                legacyBehavior>
                <Twitter size={20} />
              </Link>
              <Link
                href="#"
                className="text-neutral-400 hover:text-white transition-colors"
                legacyBehavior>
                <Facebook size={20} />
              </Link>
              <Link
                href="#"
                className="text-neutral-400 hover:text-white transition-colors"
                legacyBehavior>
                <Instagram size={20} />
              </Link>
            </div>
          </div>
        </div>
        
        <Separator className="bg-neutral-800" />
        
        <div className="flex flex-col md:flex-row justify-between items-center pt-8 text-sm text-neutral-500">
          <div className="mb-4 md:mb-0">
            &copy; {new Date().getFullYear()} {COMPANY_INFO.name}. All rights reserved.
          </div>
          <div className="flex space-x-6">
            <Link href="/privacy-policy" className="hover:text-neutral-300 transition-colors">
              Privacy Policy
            </Link>
            <Link href="/terms-of-service" className="hover:text-neutral-300 transition-colors">
              Terms of Service
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
