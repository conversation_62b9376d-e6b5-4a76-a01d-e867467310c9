'use client';

import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react';
import Link from 'next/link';

export default function CTASection() {
  return (
    <section className="py-24 relative overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-neutral-950 to-black -z-10"></div>
      {/* Accent gradient */}
      <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-indigo-500 to-transparent"></div>
      <div className="absolute inset-x-0 bottom-0 h-px bg-gradient-to-r from-transparent via-violet-500 to-transparent"></div>
      {/* Blurred gradient spots */}
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[400px] bg-indigo-500/10 rounded-full blur-3xl -z-10"></div>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="max-w-5xl mx-auto">
          <div className="relative p-8 md:p-12 lg:p-16 rounded-3xl overflow-hidden">
            {/* Inner glass background with border */}
            <div className="absolute inset-0 bg-gradient-to-br from-indigo-900/20 to-violet-900/20 backdrop-blur-sm border border-white/10 rounded-3xl -z-10"></div>
            
            {/* Animated gradient border effect */}
            <div className="absolute inset-px rounded-3xl bg-gradient-to-r from-indigo-500 to-violet-500 opacity-20 animate-pulse -z-10"></div>
            
            {/* Background dot pattern */}
            <div className="absolute inset-0 opacity-10 bg-[radial-gradient(#ffffff33_1px,transparent_1px)] [background-size:16px_16px] -z-10"></div>
            
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.7 }}
              className="text-center space-y-6"
            >
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight">
                Ready to transform your business?
              </h2>
              
              <p className="text-lg md:text-xl text-neutral-300 max-w-3xl mx-auto">
                Get in touch with our expert team today and discover how our technology solutions can drive your success
              </p>
              
              <div className="flex flex-col sm:flex-row justify-center gap-4 pt-4">
                <Button asChild size="lg" className="rounded-full px-8 bg-gradient-to-r from-indigo-500 to-violet-600 hover:from-indigo-600 hover:to-violet-700 shadow-lg hover:shadow-indigo-500/25">
                  <Link href="/contact" legacyBehavior>
                    Contact Us 
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
                
                <Button asChild variant="outline" size="lg" className="rounded-full px-8 border-neutral-700 hover:bg-neutral-800/50 hover:text-white">
                  <Link href="/services">
                    Our Services
                  </Link>
                </Button>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
}
