'use client';

import { ArrowR<PERSON>, Phone, Mail, MapPin } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { motion } from 'framer-motion';
import Link from 'next/link';

const contactMethods = [
  {
    icon: Phone,
    title: 'Call Us',
    description: 'Speak with our experts',
    action: 'tel:+1234567890',
    label: '+1 (234) 567-890'
  },
  {
    icon: Mail,
    title: 'Email Us',
    description: 'Get a detailed response',
    action: 'mailto:<EMAIL>',
    label: '<EMAIL>'
  },
  {
    icon: MapPin,
    title: 'Visit Us',
    description: 'Meet us in person',
    action: '/contact',
    label: 'Our Locations'
  }
];

export default function CTASection() {
  return (
    <section className="py-24 relative overflow-hidden bg-background">
      {/* Enhanced background effects */}
      <div className="absolute inset-0 bg-gradient-to-t from-background via-muted/10 to-background -z-10"></div>
      {/* Radial gradient overlay */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(99,102,241,0.1)_0%,transparent_70%)] -z-10"></div>
      {/* Grid pattern */}
      <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center opacity-10 -z-10"></div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="max-w-4xl mx-auto">
          {/* Main CTA Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7 }}
          >
            <Card className="glass-strong border-border/50 overflow-hidden">
              <CardContent className="p-12 text-center">
                <Badge variant="outline" className="mb-6 bg-background/50 border-border">
                  Ready to Get Started?
                </Badge>

                <h2 className="text-3xl md:text-4xl font-bold mb-6 text-gradient-brand">
                  Transform Your Business Today
                </h2>

                <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto leading-relaxed">
                  Join hundreds of businesses that trust S2P Infotech for their technology needs.
                  Let's discuss how we can accelerate your digital transformation journey.
                </p>

                <div className="flex flex-col sm:flex-row justify-center gap-4 mb-12">
                  <Button asChild size="lg" className="group bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-blue-500/25 rounded-full px-8">
                    <Link href="/contact">
                      Start Your Project
                      <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                    </Link>
                  </Button>

                  <Button asChild variant="outline" size="lg" className="rounded-full px-8 border-border hover:bg-accent hover:text-accent-foreground">
                    <Link href="/services">
                      Explore Services
                    </Link>
                  </Button>
                </div>

                {/* Contact Methods */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {contactMethods.map((method, index) => {
                    const IconComponent = method.icon;
                    return (
                      <motion.div
                        key={method.title}
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        viewport={{ once: true }}
                        transition={{ duration: 0.5, delay: 0.2 + index * 0.1 }}
                      >
                        <Card className="glass border-border/30 hover:border-border/50 transition-all duration-300 group">
                          <CardContent className="p-6 text-center">
                            <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4 group-hover:bg-primary/20 transition-colors duration-300">
                              <IconComponent className="h-6 w-6 text-primary" />
                            </div>
                            <h3 className="font-semibold text-foreground mb-2">{method.title}</h3>
                            <p className="text-sm text-muted-foreground mb-3">{method.description}</p>
                            <Button asChild variant="ghost" size="sm" className="text-primary hover:text-primary hover:bg-primary/10">
                              <Link href={method.action}>
                                {method.label}
                              </Link>
                            </Button>
                          </CardContent>
                        </Card>
                      </motion.div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Trust Indicators */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7, delay: 0.4 }}
            className="mt-12"
          >
            <Card className="glass border-border/30">
              <CardContent className="p-8">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
                  <div>
                    <div className="text-2xl font-bold text-primary mb-1">500+</div>
                    <div className="text-sm text-muted-foreground">Projects Completed</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-primary mb-1">99%</div>
                    <div className="text-sm text-muted-foreground">Client Satisfaction</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-primary mb-1">24/7</div>
                    <div className="text-sm text-muted-foreground">Support Available</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-primary mb-1">10+</div>
                    <div className="text-sm text-muted-foreground">Years Experience</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </section>
  );
}