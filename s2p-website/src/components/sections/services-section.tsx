'use client';

import { useState } from 'react';
import { 
  Settings, 
  Code, 
  Shield, 
  Server, 
  Paintbrush, 
  Eye, 
  Network, 
  Headphones,
  ArrowRight
} from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { motion } from 'framer-motion';
import { SERVICES } from '@/lib/constants';
import Link from 'next/link';

const iconMap = {
  settings: Settings,
  code: Code,
  shield: Shield,
  server: Server,
  paintbrush: Paintbrush,
  eye: Eye,
  network: Network,
  headphones: Headphones,
};

// Group services into categories for the tabs
const serviceCategories = [
  { id: 'all', label: 'All Services' },
  { id: 'consulting', label: 'Consulting' },
  { id: 'software', label: 'Software' },
  { id: 'hardware', label: 'Hardware' }
];

export default function ServicesSection() {
  const [activeCategory, setActiveCategory] = useState('all');
  
  // Filter services based on active category
  const filteredServices = activeCategory === 'all' 
    ? SERVICES 
    : SERVICES.filter(service => {
        if (activeCategory === 'consulting' && (service.title.includes('Consulting') || service.title.includes('IT Consulting')))
          return true;
        if (activeCategory === 'software' && (service.title.includes('Software') || service.title.includes('CAD')))
          return true;
        if (activeCategory === 'hardware' && (service.title.includes('Hardware') || service.title.includes('Infrastructure') || service.title.includes('Networking') || service.title.includes('Surveillance')))
          return true;
        return false;
      });

  return (
    <section className="py-24 relative overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-black to-neutral-950 -z-10"></div>
      {/* Subtle grid pattern */}
      <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center opacity-5 -z-10"></div>
      {/* Blurred gradient spots */}
      <div className="absolute top-10 left-10 w-[500px] h-[300px] bg-indigo-500/10 rounded-full blur-3xl -z-10"></div>
      <div className="absolute bottom-10 right-10 w-[500px] h-[300px] bg-violet-500/10 rounded-full blur-3xl -z-10"></div>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7 }}
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-4 tracking-tight">
              Our Services
            </h2>
            <p className="text-lg text-neutral-400 max-w-2xl mx-auto">
              Comprehensive IT solutions designed to empower your business with cutting-edge technology and innovation
            </p>
          </motion.div>
        </div>

        {/* Services Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.7, delay: 0.2 }}
          className="mb-12"
        >
          <Tabs defaultValue="all" className="w-full" onValueChange={setActiveCategory}>
            <TabsList className="grid grid-cols-2 md:grid-cols-4 w-full mb-12">
              {serviceCategories.map(category => (
                <TabsTrigger 
                  key={category.id} 
                  value={category.id}
                  className="data-[state=active]:bg-neutral-800 data-[state=active]:text-white"
                >
                  {category.label}
                </TabsTrigger>
              ))}
            </TabsList>
            
            <TabsContent value={activeCategory} className="mt-0 w-full">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredServices.map((service, index) => {
                  const IconComponent = iconMap[service.icon as keyof typeof iconMap];
                  
                  return (
                    <motion.div 
                      key={service.title}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                    >
                      <Card className="bg-neutral-900/50 border-neutral-800 hover:border-neutral-700 hover:shadow-lg hover:shadow-indigo-900/20 transition-all duration-300 h-full flex flex-col">
                        <CardHeader className="pb-2">
                          <div className="w-14 h-14 rounded-xl bg-neutral-800 flex items-center justify-center mb-4">
                            <IconComponent className="h-6 w-6 text-indigo-400" />
                          </div>
                          <CardTitle className="text-xl">{service.title}</CardTitle>
                        </CardHeader>
                        <CardContent className="text-neutral-400 text-sm flex-1">
                          <p>{service.description}</p>
                        </CardContent>
                        <CardFooter>
                          <Link
                            href={`/services#${service.title.toLowerCase().replace(/\s+/g, '-')}`}
                            className="text-indigo-400 hover:text-indigo-300 flex items-center text-sm font-medium"
                            legacyBehavior>
                            Learn more <ArrowRight className="ml-2 h-4 w-4" />
                          </Link>
                        </CardFooter>
                      </Card>
                    </motion.div>
                  );
                })}
              </div>
            </TabsContent>
          </Tabs>
        </motion.div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.7, delay: 0.4 }}
          className="text-center mt-16"
        >
          <p className="text-neutral-400 mb-6 text-lg">
            Ready to transform your business with our comprehensive IT solutions?
          </p>
          <Button asChild size="lg" className="rounded-full px-8 bg-gradient-to-r from-indigo-500 to-violet-600">
            <Link href="/services">
              Explore All Services
            </Link>
          </Button>
        </motion.div>
      </div>
    </section>
  );
}
