'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { ArrowRight, Play } from 'lucide-react';
import { COMPANY_INFO } from '@/lib/constants';
import ThreeScene from '@/components/three/scene';
import { motion } from 'framer-motion';

export default function HeroSection() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-b from-black via-neutral-900/90 to-black">
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(var(--background),0)_0%,rgba(20,20,20,0.5)_100%)] z-10"></div>
      </div>
      
      {/* Grid pattern overlay */}
      <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))] opacity-10"></div>
      
      {/* Animated dot pattern */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-20 w-64 h-64 bg-indigo-500/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute top-1/3 right-10 w-72 h-72 bg-violet-500/10 rounded-full blur-3xl animate-pulse animation-delay-2000"></div>
      </div>
      
      {/* Three.js Background */}
      {mounted && (
        <div className="absolute inset-0 opacity-30">
          <ThreeScene className="w-full h-full" />
        </div>
      )}
      
      {/* Content */}
      <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="max-w-6xl mx-auto">
          {/* Main Headline */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-white via-white to-neutral-400">
              {COMPANY_INFO.tagline}
            </h1>
          </motion.div>
          
          {/* Subtitle */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.2 }}
            className="text-center"
          >
            <p className="text-lg md:text-xl text-neutral-300 mb-12 max-w-3xl mx-auto leading-relaxed">
              {COMPANY_INFO.subtitle}
            </p>
          </motion.div>
          
          {/* Value Propositions */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.4 }}
            className="grid grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-12 max-w-5xl mx-auto"
          >
            <div className="bg-neutral-900/60 backdrop-blur-sm border border-neutral-800 rounded-xl p-6 hover:border-neutral-700 transition-all duration-300 hover:shadow-lg hover:shadow-indigo-900/20">
              <div className="text-3xl font-bold text-white mb-1">19+</div>
              <div className="text-sm text-neutral-400">Tech Partners</div>
            </div>
            
            <div className="bg-neutral-900/60 backdrop-blur-sm border border-neutral-800 rounded-xl p-6 hover:border-neutral-700 transition-all duration-300 hover:shadow-lg hover:shadow-indigo-900/20">
              <div className="text-3xl font-bold text-white mb-1">24/7</div>
              <div className="text-sm text-neutral-400">Support</div>
            </div>
            
            <div className="bg-neutral-900/60 backdrop-blur-sm border border-neutral-800 rounded-xl p-6 hover:border-neutral-700 transition-all duration-300 hover:shadow-lg hover:shadow-indigo-900/20">
              <div className="text-3xl font-bold text-white mb-1">Enterprise</div>
              <div className="text-sm text-neutral-400">Solutions</div>
            </div>
            
            <div className="bg-neutral-900/60 backdrop-blur-sm border border-neutral-800 rounded-xl p-6 hover:border-neutral-700 transition-all duration-300 hover:shadow-lg hover:shadow-indigo-900/20">
              <div className="text-3xl font-bold text-white mb-1">Expert</div>
              <div className="text-sm text-neutral-400">Team</div>
            </div>
          </motion.div>
          
          {/* Call to Action Buttons */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.6 }}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <Button size="lg" className="group bg-gradient-to-r from-indigo-500 to-violet-600 text-white hover:from-indigo-600 hover:to-violet-700 shadow-lg hover:shadow-indigo-500/25 rounded-full px-8">
              Get Started Today
              <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
            </Button>
            
            <Button variant="outline" size="lg" className="group border-neutral-700 text-white hover:bg-neutral-800/50 hover:border-neutral-600 rounded-full px-8">
              <Play className="mr-2 h-4 w-4" />
              Watch Demo
            </Button>
          </motion.div>
        </div>
      </div>
      
      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border border-neutral-700 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-white/60 rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>
  );
}
