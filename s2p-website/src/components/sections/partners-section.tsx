'use client';

import { useState } from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { 
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { PARTNERS, PARTNER_CATEGORIES } from '@/lib/constants';

export default function PartnersSection() {
  const [activeCategory, setActiveCategory] = useState('All');
  
  // Filter partners based on active category
  const filteredPartners = activeCategory === 'All' 
    ? PARTNERS 
    : PARTNERS.filter(partner => partner.category === activeCategory);

  return (
    <section className="py-24 relative overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-neutral-950 to-black -z-10"></div>
      
      {/* Dark pattern overlay */}
      <div className="absolute inset-0 opacity-10 bg-[radial-gradient(#ffffff33_1px,transparent_1px)] [background-size:16px_16px] -z-10"></div>
      
      {/* Blurred gradient spots */}
      <div className="absolute top-1/4 right-0 w-[400px] h-[300px] bg-indigo-500/5 rounded-full blur-3xl -z-10"></div>
      <div className="absolute bottom-1/4 left-0 w-[300px] h-[400px] bg-violet-500/5 rounded-full blur-3xl -z-10"></div>
      
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.7 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4 tracking-tight">
            Our Technology Partners
          </h2>
          <p className="text-lg text-neutral-400 max-w-2xl mx-auto">
            We collaborate with leading technology providers to deliver best-in-class solutions
          </p>
        </motion.div>

        {/* Partners Filter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.7, delay: 0.2 }}
        >
          <Tabs defaultValue="All" className="w-full" onValueChange={setActiveCategory}>
            <TabsList className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 w-full mb-12">
              {PARTNER_CATEGORIES.map(category => (
                <TabsTrigger 
                  key={category} 
                  value={category}
                  className="data-[state=active]:bg-neutral-800 data-[state=active]:text-white"
                >
                  {category}
                </TabsTrigger>
              ))}
            </TabsList>
            
            <TabsContent value={activeCategory} className="mt-0">
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                {filteredPartners.map((partner, index) => (
                  <motion.div 
                    key={partner.name}
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    className="group"
                  >
                    <div className="h-36 p-4 flex flex-col items-center justify-center gap-2 bg-neutral-900/50 border border-neutral-800 hover:border-neutral-700 hover:shadow-lg hover:shadow-indigo-900/10 rounded-lg transition-all duration-300">
                      <div className="relative w-20 h-20 flex items-center justify-center">
                        <Image
                          src={partner.logo}
                          alt={partner.name}
                          width={80}
                          height={80}
                          className="max-w-full max-h-full object-contain opacity-80 group-hover:opacity-100 transition-opacity duration-300"
                        />
                      </div>
                      <Badge variant="outline" className="bg-transparent border-neutral-700 text-xs font-normal">
                        {partner.category}
                      </Badge>
                    </div>
                  </motion.div>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </motion.div>
        
        {/* Mobile View Only - Featured Partners Carousel */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.7, delay: 0.4 }}
          className="mt-16 lg:hidden"
        >
          <div className="text-center mb-8">
            <h3 className="text-xl font-medium mb-2">Featured Partners</h3>
            <p className="text-sm text-neutral-400">Swipe to see our key technology partners</p>
          </div>
          
          <Carousel
            opts={{
              align: "start",
              loop: true,
            }}
            className="w-full"
          >
            <CarouselContent>
              {PARTNERS.filter((_, i) => i < 10).map((partner) => (
                <CarouselItem key={partner.name} className="md:basis-1/2 lg:basis-1/3 pl-4">
                  <div className="h-36 p-4 flex flex-col items-center justify-center gap-2 bg-neutral-900/50 border border-neutral-800 rounded-lg">
                    <div className="relative w-20 h-20 flex items-center justify-center">
                      <Image
                        src={partner.logo}
                        alt={partner.name}
                        width={80}
                        height={80}
                        className="max-w-full max-h-full object-contain opacity-80"
                      />
                    </div>
                    <Badge variant="outline" className="bg-transparent border-neutral-700 text-xs font-normal">
                      {partner.category}
                    </Badge>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
            <div className="flex justify-center mt-4">
              <CarouselPrevious className="static transform-none mx-2" />
              <CarouselNext className="static transform-none mx-2" />
            </div>
          </Carousel>
        </motion.div>
      </div>
    </section>
  );
}
